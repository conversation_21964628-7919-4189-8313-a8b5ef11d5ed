Create a single Python script that demonstrates a PID (Proportional-Integral-Derivative) controller with the following requirements:

1. Implement a real-time line graph visualization using matplotlib or similar library
2. Display two plotted lines:
   - Target setpoint (in red)
   - PID controller output (in blue)
3. Provide interactive controls:
   - Adjustable PID parameters (Kp, Ki, Kd) via input fields
   - Setpoint adjustment control
   - Reset button to clear the graph
4. Graph specifications:
   - Y-axis range: 0 to 1
   - X-axis: Time in seconds
   - Auto-scrolling display showing last 30 seconds
   - Update rate: minimum 10Hz
5. Features:
   - Real-time visualization of control response
   - Live parameter tuning
   - Clear display of current PID values
   - Smooth animation

The application should use a standard PID control algorithm and demonstrate both steady-state and dynamic response to setpoint changes. Include error handling and parameter validation.

Optional: Add export functionality to save PID parameters and response data.
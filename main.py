"""
Real-time PID Controller Demonstration with Interactive Visualization

This application demonstrates a PID (Proportional-Integral-Derivative) controller
with real-time visualization and interactive parameter tuning.

Features:
- Real-time line graph visualization
- Interactive PID parameter controls (Kp, Ki, Kd)
- Setpoint adjustment
- Auto-scrolling 30-second time window
- 10Hz update rate
- Export functionality for parameters and data
- Parameter validation and error handling

Author: Augment Agent
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.animation import FuncAnimation
import numpy as np
import threading
import queue
import time
import csv
from collections import deque
from datetime import datetime


class PIDController:
    """
    PID Controller implementation with integral windup protection.
    """

    def __init__(self, kp=1.0, ki=0.0, kd=0.0, setpoint=0.5, output_limits=(0, 1)):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.setpoint = setpoint
        self.output_limits = output_limits

        # Internal state
        self.previous_error = 0.0
        self.integral = 0.0
        self.last_time = None

        # Integral windup protection
        self.integral_limit = 1.0

    def update(self, current_value, dt=None):
        """
        Update PID controller with current process value.

        Args:
            current_value: Current process variable value
            dt: Time step (if None, calculated automatically)

        Returns:
            PID controller output
        """
        if dt is None:
            current_time = time.time()
            if self.last_time is None:
                dt = 0.1  # Default time step
            else:
                dt = current_time - self.last_time
            self.last_time = current_time

        # Calculate error
        error = self.setpoint - current_value

        # Proportional term
        proportional = self.kp * error

        # Integral term with windup protection
        self.integral += error * dt
        self.integral = max(-self.integral_limit, min(self.integral_limit, self.integral))
        integral = self.ki * self.integral

        # Derivative term
        derivative = self.kd * (error - self.previous_error) / dt if dt > 0 else 0
        self.previous_error = error

        # Calculate output
        output = proportional + integral + derivative

        # Apply output limits
        output = max(self.output_limits[0], min(self.output_limits[1], output))

        return output

    def reset(self):
        """Reset PID controller internal state."""
        self.previous_error = 0.0
        self.integral = 0.0
        self.last_time = None

    def set_parameters(self, kp=None, ki=None, kd=None, setpoint=None):
        """Update PID parameters."""
        if kp is not None:
            self.kp = max(0, kp)  # Ensure non-negative
        if ki is not None:
            self.ki = max(0, ki)
        if kd is not None:
            self.kd = max(0, kd)
        if setpoint is not None:
            self.setpoint = max(0, min(1, setpoint))  # Clamp to [0, 1]


class SystemSimulation:
    """
    Simple first-order system simulation for PID control demonstration.
    """

    def __init__(self, time_constant=2.0, initial_value=0.0):
        self.time_constant = time_constant
        self.current_value = initial_value
        self.initial_value = initial_value

    def update(self, control_input, dt=0.1):
        """
        Update system state using first-order dynamics.

        System equation: dx/dt = (-x + u) / tau
        where x is system output, u is control input, tau is time constant
        """
        # First-order system dynamics using Euler integration
        derivative = (-self.current_value + control_input) / self.time_constant
        self.current_value += derivative * dt

        # Ensure output stays within reasonable bounds
        self.current_value = max(0, min(1.2, self.current_value))

        return self.current_value

    def reset(self):
        """Reset system to initial state."""
        self.current_value = self.initial_value


class PIDVisualizerApp:
    """
    Main GUI application for PID controller visualization.
    """

    def __init__(self, root):
        self.root = root
        self.root.title("Real-time PID Controller Demonstration")
        self.root.geometry("1200x800")

        # Initialize PID controller and system
        self.pid = PIDController(kp=1.0, ki=0.1, kd=0.05, setpoint=0.5)
        self.system = SystemSimulation(time_constant=2.0)

        # Data storage for plotting (30 seconds at 10Hz = 300 points)
        self.max_points = 300
        self.time_data = deque(maxlen=self.max_points)
        self.setpoint_data = deque(maxlen=self.max_points)
        self.output_data = deque(maxlen=self.max_points)

        # Simulation control
        self.simulation_running = False
        self.simulation_thread = None
        self.data_queue = queue.Queue()

        # Timing
        self.start_time = time.time()
        self.update_interval = 100  # milliseconds (10Hz)

        # Create GUI
        self.create_widgets()
        self.setup_plot()

        # Start animation
        self.animation = FuncAnimation(
            self.fig, self.update_plot, interval=self.update_interval,
            blit=False, cache_frame_data=False
        )

        # Start simulation
        self.start_simulation()

        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """Create and layout GUI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Control panel frame
        control_frame = ttk.LabelFrame(main_frame, text="PID Controller Parameters", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # PID parameter controls
        self.create_parameter_controls(control_frame)

        # Setpoint control
        setpoint_frame = ttk.Frame(control_frame)
        setpoint_frame.pack(fill=tk.X, pady=5)

        ttk.Label(setpoint_frame, text="Setpoint:").pack(side=tk.LEFT)
        self.setpoint_var = tk.DoubleVar(value=0.5)
        self.setpoint_scale = ttk.Scale(
            setpoint_frame, from_=0.0, to=1.0, variable=self.setpoint_var,
            orient=tk.HORIZONTAL, length=200, command=self.update_setpoint
        )
        self.setpoint_scale.pack(side=tk.LEFT, padx=5)

        self.setpoint_label = ttk.Label(setpoint_frame, text="0.50")
        self.setpoint_label.pack(side=tk.LEFT, padx=5)

        # Control buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=5)

        ttk.Button(button_frame, text="Reset", command=self.reset_system).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Export Data", command=self.export_data).pack(side=tk.LEFT, padx=5)

        # Plot frame
        plot_frame = ttk.Frame(main_frame)
        plot_frame.pack(fill=tk.BOTH, expand=True)

        # Matplotlib canvas will be added in setup_plot()
        self.plot_frame = plot_frame

    def create_parameter_controls(self, parent):
        """Create PID parameter input controls."""
        # Kp control
        kp_frame = ttk.Frame(parent)
        kp_frame.pack(fill=tk.X, pady=2)

        ttk.Label(kp_frame, text="Kp (Proportional):", width=15).pack(side=tk.LEFT)
        self.kp_var = tk.DoubleVar(value=1.0)
        kp_spinbox = ttk.Spinbox(
            kp_frame, from_=0.0, to=10.0, increment=0.1, width=10,
            textvariable=self.kp_var, command=self.update_pid_parameters
        )
        kp_spinbox.pack(side=tk.LEFT, padx=5)
        kp_spinbox.bind('<KeyRelease>', lambda e: self.update_pid_parameters())

        # Ki control
        ki_frame = ttk.Frame(parent)
        ki_frame.pack(fill=tk.X, pady=2)

        ttk.Label(ki_frame, text="Ki (Integral):", width=15).pack(side=tk.LEFT)
        self.ki_var = tk.DoubleVar(value=0.1)
        ki_spinbox = ttk.Spinbox(
            ki_frame, from_=0.0, to=5.0, increment=0.01, width=10,
            textvariable=self.ki_var, command=self.update_pid_parameters
        )
        ki_spinbox.pack(side=tk.LEFT, padx=5)
        ki_spinbox.bind('<KeyRelease>', lambda e: self.update_pid_parameters())

        # Kd control
        kd_frame = ttk.Frame(parent)
        kd_frame.pack(fill=tk.X, pady=2)

        ttk.Label(kd_frame, text="Kd (Derivative):", width=15).pack(side=tk.LEFT)
        self.kd_var = tk.DoubleVar(value=0.05)
        kd_spinbox = ttk.Spinbox(
            kd_frame, from_=0.0, to=2.0, increment=0.01, width=10,
            textvariable=self.kd_var, command=self.update_pid_parameters
        )
        kd_spinbox.pack(side=tk.LEFT, padx=5)
        kd_spinbox.bind('<KeyRelease>', lambda e: self.update_pid_parameters())

    def setup_plot(self):
        """Setup matplotlib plot."""
        # Create figure and axis
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.fig.patch.set_facecolor('white')

        # Configure plot
        self.ax.set_xlim(0, 30)  # 30 second window
        self.ax.set_ylim(0, 1)
        self.ax.set_xlabel('Time (seconds)')
        self.ax.set_ylabel('Value')
        self.ax.set_title('PID Controller Real-time Response')
        self.ax.grid(True, alpha=0.3)

        # Create lines
        self.setpoint_line, = self.ax.plot([], [], 'r-', linewidth=2, label='Setpoint')
        self.output_line, = self.ax.plot([], [], 'b-', linewidth=2, label='PID Output')

        self.ax.legend(loc='upper right')

        # Embed plot in tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, self.plot_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def update_pid_parameters(self):
        """Update PID controller parameters from GUI inputs."""
        try:
            kp = max(0, self.kp_var.get())
            ki = max(0, self.ki_var.get())
            kd = max(0, self.kd_var.get())
            self.pid.set_parameters(kp=kp, ki=ki, kd=kd)
        except tk.TclError:
            # Handle invalid input gracefully
            pass

    def update_setpoint(self, value=None):
        """Update setpoint from slider."""
        try:
            setpoint = self.setpoint_var.get()
            self.pid.set_parameters(setpoint=setpoint)
            self.setpoint_label.config(text=f"{setpoint:.2f}")
        except tk.TclError:
            pass

    def simulation_loop(self):
        """Main simulation loop running in separate thread."""
        dt = 0.1  # 10Hz update rate

        while self.simulation_running:
            try:
                # Get current system output
                current_output = self.system.current_value

                # Update PID controller
                control_input = self.pid.update(current_output, dt)

                # Update system
                new_output = self.system.update(control_input, dt)

                # Calculate elapsed time
                current_time = time.time() - self.start_time

                # Put data in queue for GUI thread
                data_point = {
                    'time': current_time,
                    'setpoint': self.pid.setpoint,
                    'output': new_output
                }

                try:
                    self.data_queue.put_nowait(data_point)
                except queue.Full:
                    # Skip this data point if queue is full
                    pass

                # Sleep to maintain update rate
                time.sleep(dt)

            except Exception as e:
                print(f"Simulation error: {e}")
                break

    def start_simulation(self):
        """Start the simulation thread."""
        if not self.simulation_running:
            self.simulation_running = True
            self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
            self.simulation_thread.start()

    def stop_simulation(self):
        """Stop the simulation thread."""
        self.simulation_running = False
        if self.simulation_thread and self.simulation_thread.is_alive():
            self.simulation_thread.join(timeout=1.0)

    def update_plot(self, frame):
        """Update plot with new data (called by FuncAnimation)."""
        # Process all available data from queue
        while not self.data_queue.empty():
            try:
                data_point = self.data_queue.get_nowait()

                # Add data to deques
                self.time_data.append(data_point['time'])
                self.setpoint_data.append(data_point['setpoint'])
                self.output_data.append(data_point['output'])

            except queue.Empty:
                break

        # Update plot if we have data
        if len(self.time_data) > 0:
            # Convert to numpy arrays for plotting
            times = np.array(self.time_data)
            setpoints = np.array(self.setpoint_data)
            outputs = np.array(self.output_data)

            # Update line data
            self.setpoint_line.set_data(times, setpoints)
            self.output_line.set_data(times, outputs)

            # Update x-axis to show last 30 seconds
            if len(times) > 0:
                latest_time = times[-1]
                self.ax.set_xlim(max(0, latest_time - 30), latest_time + 1)

        return self.setpoint_line, self.output_line

    def reset_system(self):
        """Reset PID controller and system to initial state."""
        # Stop animation temporarily
        self.animation.pause()

        # Reset controllers and system
        self.pid.reset()
        self.system.reset()

        # Clear data
        self.time_data.clear()
        self.setpoint_data.clear()
        self.output_data.clear()

        # Reset start time
        self.start_time = time.time()

        # Clear plot
        self.setpoint_line.set_data([], [])
        self.output_line.set_data([], [])
        self.ax.set_xlim(0, 30)

        # Resume animation
        self.animation.resume()

        # Redraw canvas
        self.canvas.draw()

    def export_data(self):
        """Export current PID parameters and response data to CSV."""
        if len(self.time_data) == 0:
            messagebox.showwarning("No Data", "No data available to export.")
            return

        # Ask user for file location
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Export PID Data"
        )

        if filename:
            try:
                with open(filename, 'w', newline='') as csvfile:
                    writer = csv.writer(csvfile)

                    # Write header with PID parameters
                    writer.writerow([f"# PID Parameters: Kp={self.pid.kp:.3f}, Ki={self.pid.ki:.3f}, Kd={self.pid.kd:.3f}"])
                    writer.writerow([f"# Export Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
                    writer.writerow([])  # Empty row

                    # Write data header
                    writer.writerow(["Time (s)", "Setpoint", "PID Output"])

                    # Write data
                    for i in range(len(self.time_data)):
                        writer.writerow([
                            f"{self.time_data[i]:.2f}",
                            f"{self.setpoint_data[i]:.4f}",
                            f"{self.output_data[i]:.4f}"
                        ])

                messagebox.showinfo("Export Successful", f"Data exported to {filename}")

            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export data: {str(e)}")

    def on_closing(self):
        """Handle application closing."""
        # Stop simulation
        self.stop_simulation()

        # Close matplotlib
        plt.close(self.fig)

        # Destroy window
        self.root.destroy()


def main():
    """Main application entry point."""
    # Create and configure root window
    root = tk.Tk()

    # Set window icon (optional)
    try:
        # You can add an icon file here if desired
        # root.iconbitmap('icon.ico')
        pass
    except:
        pass

    # Create application
    app = PIDVisualizerApp(root)

    # Start GUI event loop
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    finally:
        # Ensure cleanup
        if hasattr(app, 'stop_simulation'):
            app.stop_simulation()


if __name__ == "__main__":
    main()

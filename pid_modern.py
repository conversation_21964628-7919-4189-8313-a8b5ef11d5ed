"""
Modern PID Controller Demonstration with CustomTkinter and Dark Mode

This application demonstrates a PID (Proportional-Integral-Derivative) controller
with modern dark mode GUI and real-time visualization using CustomTkinter and ctkchart.

Features:
- Modern dark mode interface using CustomTkinter
- Real-time line chart visualization with ctkchart
- Interactive PID parameter controls (Kp, Ki, Kd)
- Setpoint adjustment slider
- Auto-scrolling 30-second time window
- 10Hz update rate
- Professional dark theme styling

Author: Augment Agent
"""

import customtkinter as ctk
import ctkchart
import numpy as np
import threading
import queue
import time
from collections import deque


class PIDController:
    """
    PID Controller implementation with integral windup protection.
    """
    
    def __init__(self, kp=1.0, ki=0.0, kd=0.0, setpoint=0.5, output_limits=(0, 1)):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.setpoint = setpoint
        self.output_limits = output_limits
        
        # Internal state
        self.previous_error = 0.0
        self.integral = 0.0
        self.last_time = None
        
        # Integral windup protection
        self.integral_limit = 1.0
        
    def update(self, current_value, dt=None):
        """
        Update PID controller with current process value.
        
        Args:
            current_value: Current process variable value
            dt: Time step (if None, calculated automatically)
            
        Returns:
            PID controller output
        """
        if dt is None:
            current_time = time.time()
            if self.last_time is None:
                dt = 0.1  # Default time step
            else:
                dt = current_time - self.last_time
            self.last_time = current_time
        
        # Calculate error
        error = self.setpoint - current_value
        
        # Proportional term
        proportional = self.kp * error
        
        # Integral term with windup protection
        self.integral += error * dt
        self.integral = max(-self.integral_limit, min(self.integral_limit, self.integral))
        integral = self.ki * self.integral
        
        # Derivative term
        derivative = self.kd * (error - self.previous_error) / dt if dt > 0 else 0
        self.previous_error = error
        
        # Calculate output
        output = proportional + integral + derivative
        
        # Apply output limits
        output = max(self.output_limits[0], min(self.output_limits[1], output))
        
        return output
    
    def reset(self):
        """Reset PID controller internal state."""
        self.previous_error = 0.0
        self.integral = 0.0
        self.last_time = None
    
    def set_parameters(self, kp=None, ki=None, kd=None, setpoint=None):
        """Update PID parameters."""
        if kp is not None:
            self.kp = max(0, kp)  # Ensure non-negative
        if ki is not None:
            self.ki = max(0, ki)
        if kd is not None:
            self.kd = max(0, kd)
        if setpoint is not None:
            self.setpoint = max(0, min(1, setpoint))  # Clamp to [0, 1]


class SystemSimulation:
    """
    Simple first-order system simulation for PID control demonstration.
    """
    
    def __init__(self, time_constant=2.0, initial_value=0.0):
        self.time_constant = time_constant
        self.current_value = initial_value
        self.initial_value = initial_value
    
    def update(self, control_input, dt=0.1):
        """
        Update system state using first-order dynamics.
        
        System equation: dx/dt = (-x + u) / tau
        where x is system output, u is control input, tau is time constant
        """
        # First-order system dynamics using Euler integration
        derivative = (-self.current_value + control_input) / self.time_constant
        self.current_value += derivative * dt
        
        # Allow wider range for more interesting PID behavior
        self.current_value = max(-0.5, min(2.0, self.current_value))
        
        return self.current_value
    
    def reset(self):
        """Reset system to initial state."""
        self.current_value = self.initial_value


class ModernPIDApp:
    """
    Modern PID Controller GUI application using CustomTkinter and ctkchart.
    """
    
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")  # Force dark mode
        ctk.set_default_color_theme("dark-blue")  # Modern blue theme
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("Modern PID Controller - Real-time Demonstration")
        self.root.geometry("800x700")
        self.root.minsize(800, 700)
        
        # Initialize PID controller and system
        self.pid = PIDController(kp=1.0, ki=0.1, kd=0.05, setpoint=0.5)
        self.system = SystemSimulation(time_constant=2.0)
        
        # Data storage for plotting (120 seconds at 10Hz = 1200 points)
        self.max_points = 1200
        self.time_data = deque(maxlen=self.max_points)
        self.setpoint_data = deque(maxlen=self.max_points)
        self.output_data = deque(maxlen=self.max_points)
        
        # Simulation control
        self.simulation_running = False
        self.simulation_thread = None
        self.data_queue = queue.Queue()
        
        # Timing
        self.start_time = time.time()
        self.update_interval = 100  # milliseconds (10Hz)
        
        # Create GUI
        self.create_widgets()
        
        # Start simulation
        self.start_simulation()
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """Create and layout modern GUI widgets."""
        # Main container with padding
        main_container = ctk.CTkFrame(self.root)
        main_container.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Title
        title_label = ctk.CTkLabel(
            main_container, 
            text="🎛️ Modern PID Controller Dashboard",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(0, 5))
        
        # Control panel
        self.create_control_panel(main_container)
        
        # Chart area
        self.create_chart_area(main_container)
        
        # Start real-time updates
        self.update_chart()
    
    def create_control_panel(self, parent):
        """Create the control panel with PID parameters and setpoint."""
        control_frame = ctk.CTkFrame(parent)
        control_frame.pack(fill="x", pady=(0, 5))
        
        # Control panel title
        control_title = ctk.CTkLabel(
            control_frame,
            text="🔧 Control Parameters",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        control_title.pack(pady=5)
        
        # Parameters container
        # params_container = ctk.CTkFrame(control_frame)
        # params_container.pack(fill="x", padx=5, pady=(0, 5))
        
        # PID Parameters in a grid
        self.create_pid_controls(control_frame)
        
        # Setpoint control
        self.create_setpoint_control(control_frame)
        

    def create_pid_controls(self, parent):
        """Create PID parameter controls in a modern grid layout."""
        # Create grid container
        grid_frame = ctk.CTkFrame(parent)
        grid_frame.pack(fill="x", padx=5, pady=5)

        # Configure grid
        grid_frame.grid_columnconfigure((0, 1, 2), weight=1)

        # Kp Control
        kp_frame = ctk.CTkFrame(grid_frame)
        kp_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        ctk.CTkLabel(kp_frame, text="Kp (Proportional)",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(5, 5))

        self.kp_var = ctk.DoubleVar(value=1.0)
        self.kp_entry = ctk.CTkEntry(
            kp_frame, textvariable=self.kp_var, width=100,
            font=ctk.CTkFont(size=14)
        )
        self.kp_entry.pack(pady=5)
        self.kp_entry.bind('<KeyRelease>', lambda e: self.update_pid_parameters())

        self.kp_slider = ctk.CTkSlider(
            kp_frame, from_=0.0, to=5.0, variable=self.kp_var,
            command=self.update_pid_parameters, width=120
        )
        self.kp_slider.pack(pady=(5, 5))

        # Ki Control
        ki_frame = ctk.CTkFrame(grid_frame)
        ki_frame.grid(row=0, column=1, padx=10, pady=10, sticky="ew")

        ctk.CTkLabel(ki_frame, text="Ki (Integral)",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(5, 5))

        self.ki_var = ctk.DoubleVar(value=0.1)
        self.ki_entry = ctk.CTkEntry(
            ki_frame, textvariable=self.ki_var, width=100,
            font=ctk.CTkFont(size=14)
        )
        self.ki_entry.pack(pady=5)
        self.ki_entry.bind('<KeyRelease>', lambda e: self.update_pid_parameters())

        self.ki_slider = ctk.CTkSlider(
            ki_frame, from_=0.0, to=2.0, variable=self.ki_var,
            command=self.update_pid_parameters, width=120
        )
        self.ki_slider.pack(pady=(5, 5))

        # Kd Control
        kd_frame = ctk.CTkFrame(grid_frame)
        kd_frame.grid(row=0, column=2, padx=5, pady=5, sticky="ew")

        ctk.CTkLabel(kd_frame, text="Kd (Derivative)",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(5, 5))

        self.kd_var = ctk.DoubleVar(value=0.05)
        self.kd_entry = ctk.CTkEntry(
            kd_frame, textvariable=self.kd_var, width=100,
            font=ctk.CTkFont(size=14)
        )
        self.kd_entry.pack(pady=5)
        self.kd_entry.bind('<KeyRelease>', lambda e: self.update_pid_parameters())

        self.kd_slider = ctk.CTkSlider(
            kd_frame, from_=0.0, to=1.0, variable=self.kd_var,
            command=self.update_pid_parameters, width=120
        )
        self.kd_slider.pack(pady=(5, 5))

    def create_setpoint_control(self, parent):
        """Create setpoint control with modern styling."""
        setpoint_frame = ctk.CTkFrame(parent)
        setpoint_frame.pack(fill="x", padx=5, pady=(0, 5))

        ctk.CTkLabel(
            setpoint_frame,
            text="🎯 Target Setpoint",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(side="left",pady=(5, 5), padx=(20,10))

        self.setpoint_var = ctk.DoubleVar(value=0.5)

        # Setpoint entry
        self.setpoint_entry = ctk.CTkEntry(
            setpoint_frame, textvariable=self.setpoint_var, width=100,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.setpoint_entry.pack(side="left", padx=5)
        self.setpoint_entry.bind('<KeyRelease>', lambda e: self.update_setpoint())

        # Setpoint slider
        self.setpoint_slider = ctk.CTkSlider(
            setpoint_frame, from_=0.0, to=1.0, variable=self.setpoint_var,
            command=self.update_setpoint, width=300
        )
        self.setpoint_slider.pack(side="left", padx=5)

        # Reset button
        reset_btn = ctk.CTkButton(
            setpoint_frame,
            text="🔄 Reset System",
            command=self.reset_system,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=30,
            width=180
        )
        reset_btn.pack(side="left", padx=5)

    def create_chart_area(self, parent):
        """Create the chart area with ctkchart."""
        # Chart container
        chart_frame = ctk.CTkFrame(parent)
        chart_frame.pack(fill="both", expand=True)

        # Chart title
        chart_title = ctk.CTkLabel(
            chart_frame,
            text="📈 Real-time PID Response",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        chart_title.pack(pady=(5, 5))

        # Create more time axis labels to show more data points (slower scrolling)
        # ctkchart shows the most recent N points where N = len(x_axis_values)
        # At 10Hz, 300 points = 30 seconds, 600 points = 60 seconds
        num_points = 60  # Show 60 data points (6 minutes at 10Hz)
        self.time_labels = tuple(f"{i*6}s" for i in range(num_points))

        # Set Y-axis range to 0-1.2 to focus on 0-1 range with slight overshoot capability
        self.y_min = -0.1
        self.y_max = 1.1

        # Create the line chart with 0-1.2 range (responsive width)
        self.line_chart = ctkchart.CTkLineChart(
            master=chart_frame,
            x_axis_values=self.time_labels,
            y_axis_values=(self.y_min, self.y_max),
            y_axis_label_count=7,  # 0.0, 0.2, 0.4, 0.6, 0.8, 1.0, 1.2
            x_axis_section_count=6,
            y_axis_section_count=6,
            height=300,
            width=800
        )
        self.line_chart.pack(pady=5, padx=5)

        # Create lines for setpoint and output (output first, then setpoint on top)
        self.output_line = ctkchart.CTkLine(
            master=self.line_chart,
            color=("#4444ff", "#6666ff"),  # Blue for PID output (light, dark)
            size=2
        )

        self.setpoint_line = ctkchart.CTkLine(
            master=self.line_chart,
            color=("#ff4444", "#ff6666"),  # Red for setpoint (light, dark)
            size=3,
            point_highlight_color=("#ff4444", "#ff6666")
        )

        # Legend
        legend_frame = ctk.CTkFrame(chart_frame)
        legend_frame.pack(pady=(0, 5))

        ctk.CTkLabel(
            legend_frame, text="🔴 Setpoint",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#ff6666"
        ).pack(side="left", padx=20)

        ctk.CTkLabel(
            legend_frame, text="🔵 PID Output",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="#6666ff"
        ).pack(side="left", padx=20)

        # Status display
        status_frame = ctk.CTkFrame(chart_frame)
        status_frame.pack(pady=(0, 5))

        ctk.CTkLabel(
            status_frame, text="📊 Current Values",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))

        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Setpoint: 0.50 | Output: 0.00 | Range: 0.00 - 1.00 | History: 3 minutes",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=(0, 5))

    def update_pid_parameters(self, value=None):
        """Update PID controller parameters from GUI inputs."""
        try:
            kp = max(0, self.kp_var.get())
            ki = max(0, self.ki_var.get())
            kd = max(0, self.kd_var.get())
            self.pid.set_parameters(kp=kp, ki=ki, kd=kd)
        except:
            # Handle invalid input gracefully
            pass

    def update_setpoint(self, value=None):
        """Update setpoint from slider/entry."""
        try:
            setpoint = max(0, min(1, self.setpoint_var.get()))
            self.pid.set_parameters(setpoint=setpoint)
            self.setpoint_label.configure(text=f"{setpoint:.2f}")
        except:
            pass

    def get_data_statistics(self):
        """Get statistics about current data for display purposes."""
        if len(self.setpoint_data) == 0 and len(self.output_data) == 0:
            return None

        # Get all current values
        all_values = []
        if len(self.setpoint_data) > 0:
            all_values.extend(self.setpoint_data)
        if len(self.output_data) > 0:
            all_values.extend(self.output_data)

        if not all_values:
            return None

        return {
            'min': min(all_values),
            'max': max(all_values),
            'current_setpoint': self.setpoint_data[-1] if self.setpoint_data else 0,
            'current_output': self.output_data[-1] if self.output_data else 0
        }

    def simulation_loop(self):
        """Main simulation loop running in separate thread."""
        dt = 0.1  # 10Hz update rate

        while self.simulation_running:
            try:
                # Get current system output
                current_output = self.system.current_value

                # Update PID controller
                control_input = self.pid.update(current_output, dt)

                # Update system
                new_output = self.system.update(control_input, dt)

                # Calculate elapsed time
                current_time = time.time() - self.start_time

                # Put data in queue for GUI thread
                data_point = {
                    'time': current_time,
                    'setpoint': self.pid.setpoint,
                    'output': new_output
                }

                try:
                    self.data_queue.put_nowait(data_point)
                except queue.Full:
                    # Skip this data point if queue is full
                    pass

                # Sleep to maintain update rate
                time.sleep(dt)

            except Exception as e:
                print(f"Simulation error: {e}")
                break

    def start_simulation(self):
        """Start the simulation thread."""
        if not self.simulation_running:
            self.simulation_running = True
            self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
            self.simulation_thread.start()

    def stop_simulation(self):
        """Stop the simulation thread."""
        self.simulation_running = False
        if self.simulation_thread and self.simulation_thread.is_alive():
            self.simulation_thread.join(timeout=1.0)

    def update_chart(self):
        """Update chart with new data (called periodically)."""
        # Process all available data from queue
        updated = False
        while not self.data_queue.empty():
            try:
                data_point = self.data_queue.get_nowait()

                # Add data to deques
                self.time_data.append(data_point['time'])
                self.setpoint_data.append(data_point['setpoint'])
                self.output_data.append(data_point['output'])
                updated = True

            except queue.Empty:
                break

        # Update chart if we have new data
        if updated and len(self.time_data) > 0:
            # Get latest values
            latest_setpoint = self.setpoint_data[-1]
            latest_output = self.output_data[-1]

            # Clamp values to our Y-axis range
            clamped_setpoint = max(self.y_min, min(self.y_max, latest_setpoint))
            clamped_output = max(self.y_min, min(self.y_max, latest_output))

            # Show actual values on chart - ctkchart should handle scaling based on y_axis_values
            self.line_chart.show_data(line=self.setpoint_line, data=[clamped_setpoint])
            self.line_chart.show_data(line=self.output_line, data=[clamped_output])

            # Update status display
            stats = self.get_data_statistics()
            if stats:
                elapsed_minutes = len(self.time_data) / 600  # 10Hz = 600 points per minute
                status_text = (
                    f"Setpoint: {stats['current_setpoint']:.3f} | "
                    f"Output: {stats['current_output']:.3f} | "
                    f"Range: {stats['min']:.3f} - {stats['max']:.3f} | "
                    f"History: {elapsed_minutes:.1f} min ({len(self.time_data)} points)"
                )
                self.status_label.configure(text=status_text)

        # Schedule next update
        self.root.after(self.update_interval, self.update_chart)

    def reset_system(self):
        """Reset PID controller and system to initial state."""
        # Reset controllers and system
        self.pid.reset()
        self.system.reset()

        # Clear data
        self.time_data.clear()
        self.setpoint_data.clear()
        self.output_data.clear()

        # Reset start time
        self.start_time = time.time()

        # Clear chart by creating new lines
        self.setpoint_line = ctkchart.CTkLine(
            master=self.line_chart,
            color=("#ff4444", "#ff6666"),  # Red for setpoint
            size=3,
            point_highlight="enabled",
            point_highlight_color=("#ff4444", "#ff6666")
        )

        self.output_line = ctkchart.CTkLine(
            master=self.line_chart,
            color=("#4444ff", "#6666ff"),  # Blue for PID output
            size=2,
            fill="enabled"
        )

    def on_closing(self):
        """Handle application closing."""
        # Stop simulation
        self.stop_simulation()

        # Destroy window
        self.root.destroy()

    def run(self):
        """Start the application."""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
        except Exception as e:
            print(f"Application error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Ensure cleanup
            self.stop_simulation()


def main():
    """Main application entry point."""
    print("🚀 Starting Modern PID Controller Application...")
    print("📊 Features: Dark Mode UI, Real-time Charts, Interactive Controls")
    print("🎛️ Adjust PID parameters and setpoint to see real-time response!")
    print("=" * 60)

    # Create and run application
    app = ModernPIDApp()
    app.run()


if __name__ == "__main__":
    main()

"""
Modern PID Controller Demonstration with CustomTkinter and Dark Mode

This application demonstrates a PID (Proportional-Integral-Derivative) controller
with modern dark mode GUI and real-time visualization using CustomTkinter and ctkchart.

Features:
- Modern dark mode interface using CustomTkinter
- Real-time line chart visualization with ctkchart
- Interactive PID parameter controls (Kp, Ki, Kd)
- Setpoint adjustment slider
- Auto-scrolling 30-second time window
- 10Hz update rate
- Professional dark theme styling

Author: Augment Agent
"""

import customtkinter as ctk
import ctkchart
import numpy as np
import threading
import queue
import time
from collections import deque


class PIDController:
    """
    PID Controller implementation with integral windup protection.
    """
    
    def __init__(self, kp=1.0, ki=0.0, kd=0.0, setpoint=0.5, output_limits=(0, 1)):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.setpoint = setpoint
        self.output_limits = output_limits
        
        # Internal state
        self.previous_error = 0.0
        self.integral = 0.0
        self.last_time = None
        
        # Integral windup protection
        self.integral_limit = 1.0
        
    def update(self, current_value, dt=None):
        """
        Update PID controller with current process value.
        
        Args:
            current_value: Current process variable value
            dt: Time step (if None, calculated automatically)
            
        Returns:
            PID controller output
        """
        if dt is None:
            current_time = time.time()
            if self.last_time is None:
                dt = 0.1  # Default time step
            else:
                dt = current_time - self.last_time
            self.last_time = current_time
        
        # Calculate error
        error = self.setpoint - current_value
        
        # Proportional term
        proportional = self.kp * error
        
        # Integral term with windup protection
        self.integral += error * dt
        self.integral = max(-self.integral_limit, min(self.integral_limit, self.integral))
        integral = self.ki * self.integral
        
        # Derivative term
        derivative = self.kd * (error - self.previous_error) / dt if dt > 0 else 0
        self.previous_error = error
        
        # Calculate output
        output = proportional + integral + derivative
        
        # Apply output limits
        output = max(self.output_limits[0], min(self.output_limits[1], output))
        
        return output
    
    def reset(self):
        """Reset PID controller internal state."""
        self.previous_error = 0.0
        self.integral = 0.0
        self.last_time = None
    
    def set_parameters(self, kp=None, ki=None, kd=None, setpoint=None):
        """Update PID parameters."""
        if kp is not None:
            self.kp = max(0, kp)  # Ensure non-negative
        if ki is not None:
            self.ki = max(0, ki)
        if kd is not None:
            self.kd = max(0, kd)
        if setpoint is not None:
            self.setpoint = max(0, min(1, setpoint))  # Clamp to [0, 1]


class SystemSimulation:
    """
    Simple first-order system simulation for PID control demonstration.
    """
    
    def __init__(self, time_constant=2.0, initial_value=0.0):
        self.time_constant = time_constant
        self.current_value = initial_value
        self.initial_value = initial_value
    
    def update(self, control_input, dt=0.1):
        """
        Update system state using first-order dynamics.
        
        System equation: dx/dt = (-x + u) / tau
        where x is system output, u is control input, tau is time constant
        """
        # First-order system dynamics using Euler integration
        derivative = (-self.current_value + control_input) / self.time_constant
        self.current_value += derivative * dt
        
        # Ensure output stays within reasonable bounds
        self.current_value = max(0, min(1.2, self.current_value))
        
        return self.current_value
    
    def reset(self):
        """Reset system to initial state."""
        self.current_value = self.initial_value


class ModernPIDApp:
    """
    Modern PID Controller GUI application using CustomTkinter and ctkchart.
    """
    
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")  # Force dark mode
        ctk.set_default_color_theme("dark-blue")  # Modern blue theme
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("Modern PID Controller - Real-time Demonstration")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Initialize PID controller and system
        self.pid = PIDController(kp=1.0, ki=0.1, kd=0.05, setpoint=0.5)
        self.system = SystemSimulation(time_constant=2.0)
        
        # Data storage for plotting (30 seconds at 10Hz = 300 points)
        self.max_points = 300
        self.time_data = deque(maxlen=self.max_points)
        self.setpoint_data = deque(maxlen=self.max_points)
        self.output_data = deque(maxlen=self.max_points)
        
        # Simulation control
        self.simulation_running = False
        self.simulation_thread = None
        self.data_queue = queue.Queue()
        
        # Timing
        self.start_time = time.time()
        self.update_interval = 100  # milliseconds (10Hz)
        
        # Create GUI
        self.create_widgets()
        
        # Start simulation
        self.start_simulation()
        
        # Handle window closing
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """Create and layout modern GUI widgets."""
        # Main container with padding
        main_container = ctk.CTkFrame(self.root)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_container, 
            text="🎛️ Modern PID Controller Dashboard",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Control panel
        self.create_control_panel(main_container)
        
        # Chart area
        self.create_chart_area(main_container)
        
        # Start real-time updates
        self.update_chart()
    
    def create_control_panel(self, parent):
        """Create the control panel with PID parameters and setpoint."""
        control_frame = ctk.CTkFrame(parent)
        control_frame.pack(fill="x", pady=(0, 20))
        
        # Control panel title
        control_title = ctk.CTkLabel(
            control_frame,
            text="🔧 Control Parameters",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        control_title.pack(pady=15)
        
        # Parameters container
        params_container = ctk.CTkFrame(control_frame)
        params_container.pack(fill="x", padx=20, pady=(0, 15))
        
        # PID Parameters in a grid
        self.create_pid_controls(params_container)
        
        # Setpoint control
        self.create_setpoint_control(control_frame)
        
        # Reset button
        reset_btn = ctk.CTkButton(
            control_frame,
            text="🔄 Reset System",
            command=self.reset_system,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40,
            width=200
        )
        reset_btn.pack(pady=15)
